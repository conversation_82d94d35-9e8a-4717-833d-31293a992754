
class FlightChangeEvent {
  change_Severity string @description(#"The severity of the change. One of `critical`, `moderate`, `minor`."#)
  change_summary string @description(#"A short summary less than 4 words of the flight change event"#)
  agent_response string @description(#"A helpful response to the traveler includes summary of the change and resolution options. "#)
}

class HotelChangeEvent {
  change_severity string @description(#"The severity of the change. One of `critical`, `moderate`, `minor`."#)
  change_summary string @description(#"A short summary less than 4 words of the hotel change event"#)
  agent_response string @description(#"A helpful response to the traveler about the hotel change and next steps."#)
}

class HotelInTripMessage {
  formatted_message string @description(#"A formatted in-trip message for hotel stays including hotel name with emoji, check-in/out details, room type, address, and confirmation number."#)
}

function ProcessFlightChangeEvent(
    existing_booking: string, 
    change_event_details: string, 
    current_date: string, 
    self_intro: string?, 
    convo_style: string?) -> FlightChangeEvent {
  client GPT4o
  prompt #"
    {{ _.role("system")}}
    Context:
    - Today's date is {{ current_date }}. All dates are future dates unless explicitly specified.
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}

    Core Objective
     provided a flight change event, analyze the change against the existing booking and provide actionable resolution options for the user.

    Flight changes details (JSON format):
    {{ change_event_details }}
    ---
    Existing booking details (JSON format):
    {{ existing_booking }}
    ---

    Analysis Framework:
        Step 1: Change Detection & Impact Assessment
        Step 2: Impact Severity Classification
          Classify each change as:
          CRITICAL: Cancellations, major delays (>3 hours), missed connections
          MODERATE: Schedule changes 1-3 hours, involuntary reroute
          MINOR: Small delays (<1 hour), terminal changes, seat reassignment, gate changes, aircraft equipment change 
        Step 3: Generate Resolution Options
          Provide options based on change severity:
            For minor changes, provide a brief summary and no further action is required.
            For moderate changes and critical changes, provide suggested options for the traveler.
    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

function ProcessHotelChangeEvent(
    existing_booking: string, 
    change_details: string, 
    current_date: string, 
    self_intro: string?, 
    convo_style: string?) -> HotelChangeEvent {
  client GPT4o
  prompt #"
    {{ _.role("system")}}
    Context:
    - Today's date is {{ current_date }}. All dates are future dates unless explicitly specified.
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}

    Core Objective:
    Analyze a hotel booking change and provide helpful guidance to the traveler.

    Hotel change details:
    {{ change_details }}
    ---
    Existing booking details (JSON format):
    {{ existing_booking }}
    ---

    Analysis Framework:
      Step 1: Change Impact Assessment
      Step 2: Generate Suggested Options
        Provide actionable guidance for the traveler including rebooking options and support.
        if the hotel is cancelled, the suggested option is to rebook a new hotel.
        if the user is no show, tell we can check with the hotel to see if they can help with the refund.
      Step 3: Response Formatting
        Offer a helpful explanation of the hotel change and outline the next steps. Ask the user if they would like to proceed with the suggested options.
        If the user has any other preferences or thoughts, invite them to share.   
    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}

function GenerateHotelInTripMessage(
    hotel_booking: string, 
    current_date: string, 
    self_intro: string?, 
    convo_style: string?) -> HotelInTripMessage {
  client GPT4o
  prompt #"
    {{ _.role("system")}}
    Context:
    - Today's date is {{ current_date }}. All dates are future dates unless explicitly specified.
    - {{ self_intro | default(GetSelfIntro()) }}
    - {{ convo_style | default(GetConvoStyle()) }}

    Core Objective:
    Generate a formatted in-trip message for a traveler who is currently staying at a hotel.

    Hotel booking details (JSON format):
    {{ hotel_booking }}
    ---

    Message Requirements:
    1. Start with a friendly greeting acknowledging they're on their trip
    2. Include hotel name with hotel emoji (🏨)
    3. Include check-in date and time formatted as "Jun 27 at 15:00"
    4. Include check-out date and time formatted as "Jun 30 at 12:00"  
    5. Include room type if available
    6. Include hotel address if available
    7. Include confirmation/reservation number if available
    8. End with "Can I help you with something?"
    9. Use bullet points (•) for hotel details
    10. Keep the tone friendly and helpful

    Example format:
    Hello! I see you're on your trip and currently
    staying at:

    🏨 [Hotel Name]

    • Check-in: [Date] at [Time]
    • Check-out: [Date] at [Time]
    • Room: [Room Type]
    • Address: [Hotel Address]
    • Confirmation Number: [Number]

    Can I help you with something?

    Extract the following from the hotel booking JSON:
    - hotel: hotel name
    - check_in_date and check_in_time: for check-in details
    - check_out_date and check_out_time: for check-out details
    - room.option_title: for room type
    - mapMarker.address: for hotel address
    - reservation_number: for confirmation number

    {{ _.role("system")}}
    {{ ctx.output_format }}
  "#
}
