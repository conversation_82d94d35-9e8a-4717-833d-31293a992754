import json
from datetime import datetime, timedelta, timezone
from typing import Any
from zoneinfo import ZoneInfo

import dateutil.parser
from langchain_core.messages import AIMessage

from agent.agent import Agent
from baml_client import b
from baml_client.types import FlightChangeEvent, HotelChangeEvent, HotelInTripMessage
from server.database.models.bookings import Booking
from server.database.models.chat_thread import ChatThread
from server.schemas.authenticate.user import User
from server.schemas.partners.spotnana.travel_delivery import SpotnanaTravelDelivery
from server.services.memory.trips.retriever import TripMemoryRetriever
from server.services.notifications.push_app_notifications import send_event_notification
from server.utils.logger import logger
from server.utils.settings import settings
from server.utils.websocket_no_op import partial_no_op
from virtual_travel_agent.helpers import get_current_date_string
from virtual_travel_agent.langchain_chat_persistence import PostgresChatMessageHistory
from virtual_travel_agent.timings import Timings


class InTripAgent(Agent):
    def __init__(self, user: User, thread: ChatThread):
        self.user = user
        self.thread = thread
        super().__init__(
            user=user,
            history=PostgresChatMessageHistory(thread_id=thread.id),
            mem_loader=TripMemoryRetriever(user_id=str(user.id), thread_id=str(thread.id)),
            websocket_send_message=partial_no_op,
        )

    async def handle_flight_changes_for_in_trip(
        self, data: SpotnanaTravelDelivery, booking: Booking, thread: ChatThread, user: User
    ):
        payload: dict = data.payload

        try:
            is_booking_in_trip = await InTripAgent.is_in_trip_booking(booking)
            if not is_booking_in_trip:
                logger.info(f"[IN_TRIP] Flight booking {booking.id} is not in trip, skipping.")
                return

            extracted_data: dict[str, Any] = await InTripAgent.extract_event_details(payload)

            t = Timings("BAML: ProcessFlightChangeEvent")
            results: FlightChangeEvent = await b.ProcessFlightChangeEvent(
                existing_booking=json.dumps(booking.content),
                change_event_details=json.dumps(extracted_data),
                current_date=get_current_date_string(),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("green")
            logger.log_baml()

            logger.info(f"[IN_TRIP] Sending notification to user: {user.email}.")

            notificatioin_event = {
                "summary": results.change_summary,
                "description": results.agent_response,
            }
            await send_event_notification(notificatioin_event, user.email)

            msg = AIMessage(content=results.agent_response)
            msg.additional_kwargs = {
                "agent_classification": "InTrip",
                "is_in_trip_openning": True,
            }
            await self.persist_messages([msg])

        except Exception as e:
            logger.error(f"[IN_TRIP] Error handling flight changes: {str(e)}")

    async def handle_hotel_change_for_in_trip(self, hotel_booking: Booking, change: dict[str, Any], user: User):
        """Handle hotel changes from booking.com for in-trip bookings."""
        try:
            status = change.get("status", "")
            is_booking_in_trip = await InTripAgent.is_in_trip_booking(hotel_booking)
            if not is_booking_in_trip:
                logger.info(f"[IN_TRIP] Hotel booking {hotel_booking.id} is not in trip, skipping.")
                return

            t = Timings("BAML: ProcessHotelChangeEvent")
            results: HotelChangeEvent = await b.ProcessHotelChangeEvent(
                existing_booking=json.dumps(hotel_booking.content),
                change_details=json.dumps(status),
                current_date=get_current_date_string(),
                self_intro=settings.OTTO_SELF_INTRO,
                convo_style=settings.OTTO_CONVO_STYLE,
                baml_options={"collector": logger.collector},
            )
            t.print_timing("green")
            logger.log_baml()

            logger.info(f"[IN_TRIP] Sending hotel change notification to user: {user.email}.")

            notification_event = {
                "summary": results.change_summary,
                "description": results.agent_response,
            }
            await send_event_notification(notification_event, user.email)

            msg = AIMessage(content=results.agent_response)
            msg.additional_kwargs = {
                "agent_classification": "InTrip",
                "is_in_trip_openning": True,
                "change_type": "hotel",
            }
            await self.persist_messages([msg])

        except Exception as e:
            logger.error(f"[IN_TRIP] Error handling hotel change: {str(e)}")

    @staticmethod
    async def extract_event_details(resource):
        try:
            extracted_data = {}

            source_info = resource.get("sourceInfo", {})
            extracted_data["sourcePnrId"] = source_info.get("sourcePnrId")

            air_pnr = resource.get("airPnr", {})
            extracted_data["legs"] = air_pnr.get("legs", [])
            extracted_data["disruptedFlightDetails"] = air_pnr.get("disruptedFlightDetails", [])
            extracted_data["otherServiceInfos"] = air_pnr.get("otherServiceInfos", [])
            extracted_data["airPnrRemarks"] = air_pnr.get("airPnrRemarks", [])

            source_statuses = []
            for leg in extracted_data["legs"]:
                flights = leg.get("flights", [])
                for flight in flights:
                    source_status = flight.get("sourceStatus")
                    if source_status:
                        source_statuses.append(
                            {
                                "sourceStatus": source_status,
                                "flightId": flight.get("flightId"),
                                "marketing": flight.get("marketing", {}),
                            }
                        )
            extracted_data["flightSourceStatuses"] = source_statuses

            operation_summary = resource.get("operationSummary", {})
            extracted_data["pnrUpdateSummary"] = operation_summary.get("pnrUpdateSummary", {})

            extracted_data["bookingHistory"] = resource.get("bookingHistory", [])

            logger.info(f"[IN_TRIP] Successfully extracted event details: {list(extracted_data.keys())}")
            return extracted_data

        except Exception as e:
            logger.error(f"[IN_TRIP] Error extracting event details: {str(e)}")
            return {}

    @staticmethod
    async def is_in_trip_booking(existing_booking: Booking) -> bool:
        today = datetime.now(timezone.utc)

        if existing_booking.type == "flight":
            return InTripAgent._is_flight_in_trip(existing_booking, today)
        else:
            if existing_booking.start_date and existing_booking.end_date:
                start_date = existing_booking.start_date.date()
                end_date = existing_booking.end_date.date()
                return start_date <= today.date() <= end_date
            return False

    @staticmethod
    def _is_flight_in_trip(booking: Booking, current_time: datetime) -> bool:
        content = booking.content
        departure_time = None
        arrival_time = None
        departure_timezone = None
        arrival_timezone = None
        try:
            if "legs" in content:
                legs = content.get("legs", [])
                if legs:
                    first_leg = legs[0]
                    flight_segments = first_leg.get("flight_segments", [])
                    if flight_segments and len(flight_segments) > 0:
                        flight_stops = flight_segments[0].get("flight_stops", [])
                        if flight_stops and flight_stops[0].get("departure"):
                            stop = flight_stops[0]
                            departure_time = dateutil.parser.isoparse(stop.get("departure"))
                            departure_timezone = stop.get("departure_timezone")

                    last_leg = legs[-1]
                    last_flight_segments = last_leg.get("flight_segments", [])
                    if last_flight_segments and len(last_flight_segments) > 0:
                        last_flight_stops = last_flight_segments[-1].get("flight_stops", [])
                        if last_flight_stops and last_flight_stops[-1].get("arrival"):
                            last_stop = last_flight_stops[-1]
                            arrival_time = dateutil.parser.isoparse(last_stop.get("arrival"))
                            arrival_timezone = last_stop.get("arrival_timezone")
            else:
                if content.get("outbound"):
                    outbound = content.get("outbound", {})
                    outbound_flight_segments = outbound.get("flight_segments", [])
                    if outbound_flight_segments and len(outbound_flight_segments) > 0:
                        outbound_flight_stops = outbound_flight_segments[0].get("flight_stops", [])
                        if outbound_flight_stops and outbound_flight_stops[0].get("departure_time"):
                            stop = outbound_flight_stops[0]
                            departure_time = dateutil.parser.isoparse(stop.get("departure_time"))
                            departure_timezone = stop.get("departure_timezone")

                if content.get("return"):
                    return_flight = content.get("return", {})
                    return_flight_segments = return_flight.get("flight_segments", [])
                    if return_flight_segments and len(return_flight_segments) > 0:
                        return_flight_stops = return_flight_segments[-1].get("flight_stops", [])
                        if return_flight_stops and return_flight_stops[-1].get("arrival_time"):
                            last_stop = return_flight_stops[-1]
                            arrival_time = dateutil.parser.isoparse(last_stop.get("arrival_time"))
                            arrival_timezone = last_stop.get("arrival_timezone")

                if arrival_time is None and content.get("outbound"):
                    outbound = content.get("outbound", {})
                    outbound_flight_segments = outbound.get("flight_segments", [])
                    if outbound_flight_segments and len(outbound_flight_segments) > 0:
                        outbound_flight_stops = outbound_flight_segments[-1].get("flight_stops", [])
                        if outbound_flight_stops and outbound_flight_stops[-1].get("arrival_time"):
                            last_stop = outbound_flight_stops[-1]
                            arrival_time = dateutil.parser.isoparse(last_stop.get("arrival_time"))
                            arrival_timezone = last_stop.get("arrival_timezone")

            if departure_time is None:
                logger.warning(f"Could not extract departure time for flight booking {booking.id}")
                return False

            if arrival_time is None:
                logger.warning(f"Could not extract arrival time for flight booking {booking.id}")
                return False

            if departure_timezone and departure_time.tzinfo is None:
                try:
                    departure_time = departure_time.replace(tzinfo=ZoneInfo(departure_timezone))
                except Exception as e:
                    logger.warning(f"Invalid departure timezone {departure_timezone} for booking {booking.id}: {e}")

            if arrival_timezone and arrival_time.tzinfo is None:
                try:
                    arrival_time = arrival_time.replace(tzinfo=ZoneInfo(arrival_timezone))
                except Exception as e:
                    logger.warning(f"Invalid arrival timezone {arrival_timezone} for booking {booking.id}: {e}")

            if current_time.tzinfo is None:
                current_time = current_time.replace(tzinfo=timezone.utc)

            departure_time_utc = departure_time.astimezone(timezone.utc) if departure_time.tzinfo else departure_time
            arrival_time_utc = arrival_time.astimezone(timezone.utc) if arrival_time.tzinfo else arrival_time
            current_time_utc = current_time.astimezone(timezone.utc) if current_time.tzinfo else current_time

            departure_minus_72h = departure_time_utc - timedelta(hours=72)
            is_in_trip = departure_minus_72h <= current_time_utc <= arrival_time_utc
            return is_in_trip

        except Exception as e:
            logger.error(f"Error checking flight in-trip status for booking {booking.id}: {str(e)}")
            return False

    @staticmethod
    def _extract_flight_details(booking: Booking) -> dict[str, Any] | None:
        """Extract flight details needed for in-trip message generation from all legs."""
        try:
            content = booking.content
            details = {}
            all_legs_info = []

            details["confirmation_number"] = content.get("airline_confirmation_number", "")

            if "legs" in content:
                legs = content.get("legs", [])
                for leg_index, leg in enumerate(legs):
                    flight_segments = leg.get("flight_segments", [])
                    for segment_index, segment in enumerate(flight_segments):
                        flight_stops = segment.get("flight_stops", [])
                        for stop_index, stop in enumerate(flight_stops):
                            leg_info = {
                                "leg_number": leg_index + 1,
                                "segment_number": segment_index + 1,
                                "stop_number": stop_index + 1,
                                "origin_code": stop.get("origin_code", ""),
                                "destination_code": stop.get("destination_code", ""),
                                "airline_code": stop.get("airline_code", ""),
                                "flight_number": stop.get("flight_number", ""),
                                "departure": stop.get("departure", ""),
                                "arrival": stop.get("arrival", ""),
                                "seat": stop.get("seat", ""),
                                "gate": stop.get("gate", ""),
                                "terminal": stop.get("terminal", ""),
                                "departure_timezone": stop.get("departure_timezone", ""),
                                "arrival_timezone": stop.get("arrival_timezone", ""),
                            }
                            all_legs_info.append(leg_info)

                if all_legs_info:
                    first_leg = all_legs_info[0]
                    last_leg = all_legs_info[-1]
                    details.update(
                        {
                            "origin_code": first_leg["origin_code"],
                            "destination_code": last_leg["destination_code"],
                            "airline_code": first_leg["airline_code"],
                            "flight_number": first_leg["flight_number"],
                            "departure": first_leg["departure"],
                            "arrival": last_leg["arrival"],
                            "seat": first_leg["seat"],
                            "gate": first_leg["gate"],
                            "terminal": first_leg["terminal"],
                            "departure_timezone": first_leg["departure_timezone"],
                            "arrival_timezone": last_leg["arrival_timezone"],
                        }
                    )

            elif content.get("outbound"):
                outbound = content.get("outbound", {})
                flight_segments = outbound.get("flight_segments", [])
                for segment_index, segment in enumerate(flight_segments):
                    flight_stops = segment.get("flight_stops", [])
                    for stop_index, stop in enumerate(flight_stops):
                        leg_info = {
                            "leg_number": 1,
                            "segment_number": segment_index + 1,
                            "stop_number": stop_index + 1,
                            "origin_code": stop.get("origin", ""),
                            "destination_code": stop.get("destination", ""),
                            "airline_code": stop.get("airline_code", ""),
                            "flight_number": stop.get("flight_number", ""),
                            "departure": stop.get("departure_time", ""),
                            "arrival": stop.get("arrival_time", ""),
                            "seat": stop.get("seat_number", ""),
                            "departure_timezone": stop.get("departure_timezone", ""),
                            "arrival_timezone": stop.get("arrival_timezone", ""),
                        }
                        all_legs_info.append(leg_info)

                if content.get("return"):
                    return_flight = content.get("return", {})
                    return_flight_segments = return_flight.get("flight_segments", [])
                    for segment_index, segment in enumerate(return_flight_segments):
                        flight_stops = segment.get("flight_stops", [])
                        for stop_index, stop in enumerate(flight_stops):
                            leg_info = {
                                "leg_number": 2,
                                "segment_number": segment_index + 1,
                                "stop_number": stop_index + 1,
                                "origin_code": stop.get("origin", ""),
                                "destination_code": stop.get("destination", ""),
                                "airline_code": stop.get("airline_code", ""),
                                "flight_number": stop.get("flight_number", ""),
                                "departure": stop.get("departure_time", ""),
                                "arrival": stop.get("arrival_time", ""),
                                "seat": stop.get("seat_number", ""),
                                "departure_timezone": stop.get("departure_timezone", ""),
                                "arrival_timezone": stop.get("arrival_timezone", ""),
                            }
                            all_legs_info.append(leg_info)

                if all_legs_info:
                    first_leg = all_legs_info[0]
                    last_leg = all_legs_info[-1]
                    details.update(
                        {
                            "origin_code": first_leg["origin_code"],
                            "destination_code": last_leg["destination_code"],
                            "airline_code": first_leg["airline_code"],
                            "flight_number": first_leg["flight_number"],
                            "departure": first_leg["departure"],
                            "arrival": last_leg["arrival"],
                            "seat": first_leg["seat"],
                            "departure_timezone": first_leg["departure_timezone"],
                            "arrival_timezone": last_leg["arrival_timezone"],
                        }
                    )

            details["all_legs"] = all_legs_info

            return details if details.get("departure") else None

        except Exception as e:
            logger.error(f"Error extracting flight details for booking {booking.id}: {str(e)}")
            return None

    @staticmethod
    def _generate_flight_in_trip_message(flight_details: dict[str, Any], departure_time: datetime) -> str:
        """Generate formatted in-trip message for flight notifications."""
        try:
            airline_code = flight_details.get("airline_code", "")
            flight_number = flight_details.get("flight_number", "")
            flight_id = f"{airline_code} {flight_number}" if airline_code and flight_number else "Flight"

            origin = flight_details.get("origin_code", "")
            destination = flight_details.get("destination_code", "")
            route = f"{origin} → {destination}" if origin and destination else ""

            departure_str = departure_time.strftime("%H:%M")

            boarding_time = departure_time - timedelta(minutes=40)
            boarding_str = boarding_time.strftime("%H:%M")

            current_time = datetime.now(departure_time.tzinfo)
            boarding_minutes_left = (boarding_time - current_time).total_seconds() / 60

            message_parts = [
                "Hello! I see you're currently on this trip, and your",
                "next flight is scheduled to depart today:",
                "",
                f"✈️ {route} ({flight_id})",
                "",
                "• Status: On time",
            ]

            if boarding_time > current_time:
                hours = int(boarding_minutes_left // 60)
                minutes = int(boarding_minutes_left % 60)
                if hours > 0:
                    message_parts.append(f"• Boarding: {boarding_str} (in {hours} hrs {minutes} mins)")
                else:
                    message_parts.append(f"• Boarding: {boarding_str} (in {minutes} mins)")
            else:
                message_parts.append(f"• Boarding: {boarding_str}")

            message_parts.append(f"• Departure: {departure_str}")

            if flight_details.get("arrival"):
                try:
                    arrival_time = dateutil.parser.isoparse(flight_details["arrival"])
                    arrival_str = arrival_time.strftime("%H:%M")
                    message_parts.append(f"• Arrival: {arrival_str}")
                except Exception:
                    estimated_arrival = departure_time + timedelta(hours=2)
                    message_parts.append(f"• Arrival: {estimated_arrival.strftime('%H:%M')}")
            else:
                estimated_arrival = departure_time + timedelta(hours=2)
                message_parts.append(f"• Arrival: {estimated_arrival.strftime('%H:%M')}")

            if flight_details.get("seat"):
                message_parts.append(f"• Seat: {flight_details['seat']}")

            if flight_details.get("terminal"):
                message_parts.append(f"• Terminal: Terminal {flight_details['terminal']}")

            if flight_details.get("gate"):
                message_parts.append(f"• Gate: {flight_details['gate']}")

            if flight_details.get("confirmation_number"):
                message_parts.append(f"• Confirmation Number: {flight_details['confirmation_number']}")

            message_parts.extend(["", "Can I help you with something?"])

            return "\n".join(message_parts)

        except Exception as e:
            logger.error(f"Error generating flight in-trip message: {str(e)}")
            return "Hello! I see you're currently on this trip. Can I help you with something?"

    @staticmethod
    async def get_in_trip_status_and_message(existing_booking: Booking) -> tuple[bool, str | None]:
        """Get both in-trip status and formatted message for frontend."""
        today = datetime.now(timezone.utc)

        if existing_booking.type == "flight":
            flight_details = InTripAgent._extract_flight_details(existing_booking)
            if flight_details:
                is_in_trip = InTripAgent._is_flight_in_trip(existing_booking, today)
                if is_in_trip:
                    try:
                        departure_time = dateutil.parser.isoparse(flight_details["departure"])
                        message = InTripAgent._generate_flight_in_trip_message(flight_details, departure_time)
                        return True, message
                    except Exception as e:
                        logger.error(f"Error generating flight message: {str(e)}")
                        return True, None
            return False, None
        elif existing_booking.type == "accommodations":
            if existing_booking.start_date and existing_booking.end_date:
                start_date = existing_booking.start_date.date()
                end_date = existing_booking.end_date.date()
                is_in_trip = start_date <= today.date() <= end_date
                if is_in_trip:
                    try:
                        local_logger = logger
                        t = Timings("BAML: GenerateHotelInTripMessage")
                        result: HotelInTripMessage = await b.GenerateHotelInTripMessage(
                            hotel_booking=json.dumps(existing_booking.content),
                            current_date=get_current_date_string(),
                            self_intro=settings.OTTO_SELF_INTRO,
                            convo_style=settings.OTTO_CONVO_STYLE,
                            baml_options={"collector": local_logger.collector},
                        )
                        t.print_timing("green")
                        local_logger.log_baml()
                        return True, result.formatted_message
                    except Exception as e:
                        logger.error(f"Error generating hotel BAML message: {str(e)}")
                        return True, None
            return False, None
        else:
            if existing_booking.start_date and existing_booking.end_date:
                start_date = existing_booking.start_date.date()
                end_date = existing_booking.end_date.date()
                is_in_trip = start_date <= today.date() <= end_date
                return is_in_trip, None
            return False, None

    async def run(self, message=None, message_type="text", extra_payload=None):
        raise NotImplementedError
